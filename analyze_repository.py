#!/usr/bin/env python3
"""
Repository Analysis Script for Feature Development Metrics
Analyzes git repository to extract historical feature development data
for use in future project estimations.
"""

import subprocess
import csv
import re
import json
from datetime import datetime, timedelta
from collections import defaultdict
import os
import sys

class RepositoryAnalyzer:
    def __init__(self, repo_path="."):
        self.repo_path = repo_path
        self.features = []
        self.jira_pattern = r'ONE-\d+'
        
    def run_git_command(self, command):
        """Execute git command and return output"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=self.repo_path,
                capture_output=True, 
                text=True, 
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            print(f"Error running command: {command}")
            print(f"Error: {e.stderr}")
            return ""
    
    def extract_jira_tickets(self, text):
        """Extract JIRA ticket IDs from text"""
        matches = re.findall(self.jira_pattern, text)
        return list(set(matches))  # Remove duplicates
    
    def get_feature_branches(self):
        """Get all feature branches from remote"""
        command = "git for-each-ref --format='%(refname:short)|%(committerdate:iso8601)|%(authorname)|%(subject)|%(objectname)' refs/remotes/origin/feature/ refs/remotes/origin/bugfix/"
        output = self.run_git_command(command)
        
        branches = []
        for line in output.split('\n'):
            if line.strip():
                parts = line.split('|')
                if len(parts) >= 5:
                    branches.append({
                        'branch_name': parts[0],
                        'last_commit_date': parts[1],
                        'author': parts[2],
                        'subject': parts[3],
                        'commit_hash': parts[4]
                    })
        return branches
    
    def get_branch_commits(self, branch_name):
        """Get all commits for a specific branch"""
        # Get commits that are in this branch but not in develop
        command = f"git log origin/{branch_name.replace('origin/', '')} --not origin/develop --pretty=format:'%h|%an|%ad|%s' --date=iso"
        output = self.run_git_command(command)
        
        commits = []
        for line in output.split('\n'):
            if line.strip():
                parts = line.split('|', 3)
                if len(parts) >= 4:
                    commits.append({
                        'hash': parts[0],
                        'author': parts[1],
                        'date': parts[2],
                        'message': parts[3]
                    })
        return commits
    
    def get_commit_stats(self, commit_hash):
        """Get file change statistics for a commit"""
        command = f"git show --stat --format='' {commit_hash}"
        output = self.run_git_command(command)
        
        files_changed = 0
        lines_added = 0
        lines_deleted = 0
        
        for line in output.split('\n'):
            if '|' in line and ('+' in line or '-' in line):
                files_changed += 1
                # Extract additions and deletions
                parts = line.split('|')
                if len(parts) > 1:
                    changes = parts[1].strip()
                    plus_count = changes.count('+')
                    minus_count = changes.count('-')
                    lines_added += plus_count
                    lines_deleted += minus_count
        
        return {
            'files_changed': files_changed,
            'lines_added': lines_added,
            'lines_deleted': lines_deleted
        }
    
    def get_branch_file_changes(self, branch_name):
        """Get comprehensive file changes for a branch"""
        command = f"git diff --stat origin/develop...origin/{branch_name.replace('origin/', '')}"
        output = self.run_git_command(command)
        
        total_files = 0
        total_added = 0
        total_deleted = 0
        
        for line in output.split('\n'):
            if 'files changed' in line or 'file changed' in line:
                # Parse summary line like "X files changed, Y insertions(+), Z deletions(-)"
                parts = line.split(',')
                for part in parts:
                    if 'file' in part and 'changed' in part:
                        total_files = int(re.findall(r'\d+', part)[0])
                    elif 'insertion' in part:
                        nums = re.findall(r'\d+', part)
                        if nums:
                            total_added = int(nums[0])
                    elif 'deletion' in part:
                        nums = re.findall(r'\d+', part)
                        if nums:
                            total_deleted = int(nums[0])
        
        return {
            'total_files_changed': total_files,
            'total_lines_added': total_added,
            'total_lines_deleted': total_deleted
        }
    
    def identify_modules(self, branch_name):
        """Identify affected modules/components"""
        command = f"git diff --name-only origin/develop...origin/{branch_name.replace('origin/', '')}"
        output = self.run_git_command(command)
        
        modules = set()
        for file_path in output.split('\n'):
            if file_path.strip():
                # Extract module from file path
                parts = file_path.split('/')
                if len(parts) > 1:
                    if parts[0] in ['app', 'core', 'feature', 'data', 'domain', 'presentation']:
                        if len(parts) > 2:
                            modules.add(f"{parts[0]}/{parts[1]}")
                        else:
                            modules.add(parts[0])
                    elif parts[0] == 'src' and len(parts) > 3:
                        modules.add(f"{parts[2]}/{parts[3]}" if len(parts) > 3 else parts[2])
                    else:
                        modules.add(parts[0])
        
        return list(modules)
    
    def calculate_development_time(self, commits):
        """Calculate development timeline from commits"""
        if not commits:
            return None, None, 0
        
        dates = [datetime.fromisoformat(commit['date'].replace('Z', '+00:00').replace(' ', 'T')) for commit in commits]
        dates.sort()
        
        start_date = dates[0]
        end_date = dates[-1]
        total_days = (end_date - start_date).days + 1  # +1 to include both start and end days
        
        return start_date, end_date, total_days
    
    def get_team_info(self, commits):
        """Extract team information from commits"""
        authors = set()
        for commit in commits:
            authors.add(commit['author'])
        
        return {
            'team_size': len(authors),
            'developers': list(authors),
            'primary_developer': max(set(commit['author'] for commit in commits), 
                                   key=lambda x: sum(1 for c in commits if c['author'] == x)) if commits else None
        }
    
    def calculate_productivity_factor(self, feature_data):
        """Calculate Productivity Factor (PF) for UCP estimation"""


        team_effort = feature_data['team_size'] * feature_data['total_dev_days']
        review_effort = feature_data.get('review_time_days', 0)
        testing_effort = feature_data.get('testing_time_days', 0)
        fix_effort = feature_data.get('post_release_fixes', 0)

        pf = 6 * (team_effort + review_effort + testing_effort) + 4 * fix_effort
        return round(pf, 2)

    def categorize_feature_size(self, feature_data):
        """Categorize feature by size"""
        days = feature_data['total_dev_days']
        lines = feature_data['lines_added']
        files = feature_data['files_changed']

        if days <= 3 and lines < 100 and files <= 5:
            return 'Small'
        elif days <= 15 and lines < 500 and files <= 30:
            return 'Medium'
        else:
            return 'Large'

    def analyze_features(self):
        """Main analysis function"""
        print("Analyzing repository for feature development metrics...")

        branches = self.get_feature_branches()
        print(f"Found {len(branches)} feature/bugfix branches")

        for i, branch in enumerate(branches):
            print(f"Processing branch {i+1}/{len(branches)}: {branch['branch_name']}")

            # Extract JIRA ticket
            jira_tickets = self.extract_jira_tickets(branch['branch_name'] + ' ' + branch['subject'])
            jira_ticket = jira_tickets[0] if jira_tickets else 'UNKNOWN'

            # Get branch commits
            commits = self.get_branch_commits(branch['branch_name'])

            if not commits:
                continue

            # Calculate timeline
            start_date, end_date, total_days = self.calculate_development_time(commits)

            # Get team info
            team_info = self.get_team_info(commits)

            # Get file changes
            file_changes = self.get_branch_file_changes(branch['branch_name'])

            # Identify modules
            modules = self.identify_modules(branch['branch_name'])

            # Extract feature description
            feature_description = branch['subject']

            # Calculate complexity score (enhanced heuristic)
            complexity_score = min(10, max(1,
                (file_changes['total_files_changed'] * 0.1) +
                (file_changes['total_lines_added'] / 100) +
                (len(modules) * 0.5) +
                (total_days * 0.1) +
                (team_info['team_size'] * 0.2)  # Team coordination complexity
            ))

            feature_data = {
                'jira_ticket': jira_ticket,
                'feature_description': feature_description,
                'module_component': ';'.join(modules) if modules else 'UNKNOWN',
                'development_start_date': start_date.strftime('%Y-%m-%d') if start_date else '',
                'development_end_date': end_date.strftime('%Y-%m-%d') if end_date else '',
                'total_dev_days': total_days,
                'lines_added': file_changes['total_lines_added'],
                'lines_deleted': file_changes['total_lines_deleted'],
                'lines_modified': file_changes['total_lines_added'] + file_changes['total_lines_deleted'],
                'total_line_count': file_changes['total_lines_added'] + file_changes['total_lines_deleted'],
                'files_changed': file_changes['total_files_changed'],
                'commits_count': len(commits),
                'pr_merge_date': branch['last_commit_date'][:10],  # Extract date part
                'branch_name': branch['branch_name'],
                'complexity_score': round(complexity_score, 2),
                'team_size': team_info['team_size'],
                'developer_names': ';'.join(team_info['developers']),
                'primary_developer': team_info['primary_developer'],
                'review_time_days': 0,  # Would need PR data to calculate
                'testing_time_days': 0,  # Would need additional data
                'deployment_date': '',  # Would need deployment logs
                'rollback_incidents': 0,  # Would need incident data
                'post_release_fixes': 0,  # Would need to analyze follow-up commits
                'feature_size_category': '',  # Will be calculated below
                'productivity_factor': 0,  # Will be calculated below
                'modules_count': len(modules),
                'cross_module_feature': 'Yes' if len(modules) > 1 else 'No',
                'commits_per_day': round(len(commits) / max(1, total_days), 2),
                'lines_per_commit': round(file_changes['total_lines_added'] / max(1, len(commits)), 2)
            }

            # Calculate derived metrics
            feature_data['feature_size_category'] = self.categorize_feature_size(feature_data)
            feature_data['productivity_factor'] = self.calculate_productivity_factor(feature_data)

            self.features.append(feature_data)

        print(f"Analysis complete. Found {len(self.features)} features.")
        return self.features

def main():
    analyzer = RepositoryAnalyzer()
    features = analyzer.analyze_features()

    # Write to enhanced CSV
    csv_filename = 'feature_metrics_analysis_enhanced.csv'
    fieldnames = [
        'jira_ticket', 'feature_description', 'module_component',
        'development_start_date', 'development_end_date', 'total_dev_days',
        'lines_added', 'lines_deleted', 'lines_modified', 'total_line_count',
        'files_changed', 'commits_count', 'pr_merge_date', 'branch_name',
        'complexity_score', 'team_size', 'developer_names', 'primary_developer',
        'review_time_days', 'testing_time_days', 'deployment_date',
        'rollback_incidents', 'post_release_fixes', 'feature_size_category',
        'productivity_factor', 'modules_count', 'cross_module_feature',
        'commits_per_day', 'lines_per_commit'
    ]

    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(features)

    print(f"Enhanced results written to {csv_filename}")
    print(f"Total features analyzed: {len(features)}")

    # Generate enhanced summary statistics
    if features:
        avg_dev_days = sum(f['total_dev_days'] for f in features) / len(features)
        avg_lines_added = sum(f['lines_added'] for f in features) / len(features)
        avg_files_changed = sum(f['files_changed'] for f in features) / len(features)
        avg_team_size = sum(f['team_size'] for f in features) / len(features)
        avg_productivity_factor = sum(f['productivity_factor'] for f in features) / len(features)

        # Feature size distribution
        size_counts = {}
        for f in features:
            size = f['feature_size_category']
            size_counts[size] = size_counts.get(size, 0) + 1

        # Cross-module features
        cross_module_count = sum(1 for f in features if f['cross_module_feature'] == 'Yes')

        print("\nSummary Statistics:")
        print(f"Average development days: {avg_dev_days:.1f}")
        print(f"Average lines added: {avg_lines_added:.0f}")
        print(f"Average files changed: {avg_files_changed:.1f}")
        print(f"Average team size: {avg_team_size:.1f}")
        print(f"Average productivity factor: {avg_productivity_factor:.1f} hours")
        print("\nFeature Size Distribution:")
        for size, count in size_counts.items():
            percentage = (count / len(features)) * 100
            print(f"  {size}: {count} features ({percentage:.1f}%)")
        print(f"\nCross-module features: {cross_module_count}/{len(features)} ({(cross_module_count/len(features)*100):.1f}%)")

if __name__ == "__main__":
    main()
